-------------------------------------------------------------------------------
Test set: com.teammanage.util.StatusCodeConverterTest
-------------------------------------------------------------------------------
Tests run: 4, Failures: 0, Errors: 2, Skipped: 0, Time elapsed: 0.026 s <<< FAILURE! -- in com.teammanage.util.StatusCodeConverterTest
com.teammanage.util.StatusCodeConverterTest.testSubscriptionStatusConverter -- Time elapsed: 0.004 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "java.lang.Integer.intValue()" because "code" is null
	at com.teammanage.constants.SubscriptionStatusConstants.isActive(SubscriptionStatusConstants.java:104)
	at com.teammanage.util.StatusCodeConverter$SubscriptionStatusConverter.isActive(StatusCodeConverter.java:166)
	at com.teammanage.util.StatusCodeConverterTest.testSubscriptionStatusConverter(StatusCodeConverterTest.java:110)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

com.teammanage.util.StatusCodeConverterTest.testInvitationStatusConverter -- Time elapsed: 0.004 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "java.lang.Integer.intValue()" because "code" is null
	at com.teammanage.constants.InvitationStatusConstants.canBeResponded(InvitationStatusConstants.java:114)
	at com.teammanage.util.StatusCodeConverter$InvitationStatusConverter.canBeResponded(StatusCodeConverter.java:111)
	at com.teammanage.util.StatusCodeConverterTest.testInvitationStatusConverter(StatusCodeConverterTest.java:75)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

