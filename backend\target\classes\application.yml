server:
  port: 8080
  servlet:
    context-path: /api/v1

spring:
  application:
    name: team-manage

  datasource:
    driver-class-name: org.mariadb.jdbc.Driver
    url: ${DATABASE_URL:****************************************************************************************************************}
    username: ${DATABASE_USERNAME:root}
    password: ${DATABASE_PASSWORD:a228702862.}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # Caffeine缓存配置（通过Java配置类管理）
  cache:
    type: caffeine

  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      logic-delete-field: isDeleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      id-type: auto
  mapper-locations: classpath*:/mapper/**/*.xml
  type-handlers-package: com.teammanage.enums

jwt:
  secret: ${JWT_SECRET:mySecretKeyForTeamManageSystemVeryLongAndSecure123456789}
  token-expiration: 604800  # 7天 (秒)

logging:
  level:
    com.teammanage: INFO
    org.springframework.security: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"



# 应用自定义配置
app:
  # 会话管理配置
  session:
    max-concurrent-sessions: 5  # 单用户最大并发会话数
    cleanup-interval: 3600      # 会话清理间隔(秒)

  # 安全配置
  security:
    password-min-length: 8
    max-login-attempts: 5
    lockout-duration: 1800      # 账号锁定时长(秒)
    # 令牌加密配置
    token:
      # AES-256加密密钥（Base64编码）- 生产环境请使用环境变量
      encryption-key: ${TOKEN_ENCRYPTION_KEY:mi9diBMqX4757+rSoa3UVt5rfRzhqmRrnINlkvdFbPg=}
      # HMAC-SHA256签名密钥（Base64编码）- 生产环境请使用环境变量
      hmac-key: ${TOKEN_HMAC_KEY:6gy5Gbd8Orp0sD7+z6oxi3RdpyJz7jEQrNH0d1CTbUw=}

  # 团队配置
  team:
    max-members: 100            # 团队最大成员数
    name-max-length: 100

  # 邀请配置
  invitation:
    expire-hours: 72            # 邀请链接有效期(小时)
    base-url: ${INVITATION_BASE_URL:}  # 前端基础URL

  # 速率限制配置
  rate-limit:
    api-calls-per-minute: 60    # 每分钟API调用次数限制
    invitations-per-hour: 10    # 每小时邀请发送次数限制
    team-creation-per-day: 5    # 每天团队创建次数限制
    login-attempts-per-hour: 10 # 每小时登录尝试次数限制

  # 验证码配置
  verification:
    code-length: 6              # 验证码长度
    expire-minutes: 5           # 验证码过期时间(分钟)
    max-attempts: 3             # 最大验证尝试次数
    resend-interval: 60         # 重发间隔(秒)
