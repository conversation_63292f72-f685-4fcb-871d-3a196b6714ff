-------------------------------------------------------------------------------
Test set: com.teammanage.service.TeamInvitationServiceTest
-------------------------------------------------------------------------------
Tests run: 2, Failures: 0, Errors: 1, Skipped: 0, Time elapsed: 16.70 s <<< FAILURE! -- in com.teammanage.service.TeamInvitationServiceTest
com.teammanage.service.TeamInvitationServiceTest.testInvitationTokenGeneration -- Time elapsed: 0.087 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.teammanage.mapper.TeamInvitationMapper.selectById(java.io.Serializable)" because "this.teamInvitationMapper" is null
	at com.teammanage.service.TeamInvitationService.generateInvitationToken(TeamInvitationService.java:477)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at com.teammanage.service.TeamInvitationServiceTest.testInvitationTokenGeneration(TeamInvitationServiceTest.java:33)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

