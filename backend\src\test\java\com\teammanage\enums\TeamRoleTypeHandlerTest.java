package com.teammanage.enums;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import org.apache.ibatis.type.JdbcType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

/**
 * TeamRole TypeHandler 测试
 */
class TeamRoleTypeHandlerTest {

    private TeamRole.TeamRoleTypeHandler typeHandler;
    private PreparedStatement preparedStatement;
    private ResultSet resultSet;

    @BeforeEach
    void setUp() {
        typeHandler = new TeamRole.TeamRoleTypeHandler();
        preparedStatement = mock(PreparedStatement.class);
        resultSet = mock(ResultSet.class);
    }

    @Test
    void testSetNonNullParameter_TeamCreator() throws SQLException {
        // 测试设置团队创建者角色
        typeHandler.setNonNullParameter(preparedStatement, 1, TeamRole.TEAM_CREATOR, JdbcType.INTEGER);
        
        // 验证设置的是权限级别值 100
        Mockito.verify(preparedStatement).setInt(1, 100);
    }

    @Test
    void testSetNonNullParameter_TeamMember() throws SQLException {
        // 测试设置团队成员角色
        typeHandler.setNonNullParameter(preparedStatement, 1, TeamRole.TEAM_MEMBER, JdbcType.INTEGER);
        
        // 验证设置的是权限级别值 10
        Mockito.verify(preparedStatement).setInt(1, 10);
    }

    @Test
    void testGetNullableResult_ByColumnName_TeamCreator() throws SQLException {
        // 模拟数据库返回 100
        when(resultSet.getInt("role")).thenReturn(100);
        when(resultSet.wasNull()).thenReturn(false);
        
        TeamRole result = typeHandler.getNullableResult(resultSet, "role");
        
        assertEquals(TeamRole.TEAM_CREATOR, result);
    }

    @Test
    void testGetNullableResult_ByColumnName_TeamMember() throws SQLException {
        // 模拟数据库返回 10
        when(resultSet.getInt("role")).thenReturn(10);
        when(resultSet.wasNull()).thenReturn(false);
        
        TeamRole result = typeHandler.getNullableResult(resultSet, "role");
        
        assertEquals(TeamRole.TEAM_MEMBER, result);
    }

    @Test
    void testGetNullableResult_ByColumnName_Null() throws SQLException {
        // 模拟数据库返回 null
        when(resultSet.getInt("role")).thenReturn(0);
        when(resultSet.wasNull()).thenReturn(true);
        
        TeamRole result = typeHandler.getNullableResult(resultSet, "role");
        
        assertNull(result);
    }

    @Test
    void testGetNullableResult_ByColumnName_InvalidCode() throws SQLException {
        // 模拟数据库返回无效的代码
        when(resultSet.getInt("role")).thenReturn(999);
        when(resultSet.wasNull()).thenReturn(false);
        
        TeamRole result = typeHandler.getNullableResult(resultSet, "role");
        
        assertNull(result); // 无效代码应该返回 null
    }

    @Test
    void testGetNullableResult_ByColumnIndex_TeamCreator() throws SQLException {
        // 模拟数据库返回 100
        when(resultSet.getInt(1)).thenReturn(100);
        when(resultSet.wasNull()).thenReturn(false);
        
        TeamRole result = typeHandler.getNullableResult(resultSet, 1);
        
        assertEquals(TeamRole.TEAM_CREATOR, result);
    }

    @Test
    void testGetNullableResult_ByColumnIndex_TeamMember() throws SQLException {
        // 模拟数据库返回 10
        when(resultSet.getInt(1)).thenReturn(10);
        when(resultSet.wasNull()).thenReturn(false);
        
        TeamRole result = typeHandler.getNullableResult(resultSet, 1);
        
        assertEquals(TeamRole.TEAM_MEMBER, result);
    }

    @Test
    void testGetNullableResult_ByColumnIndex_Null() throws SQLException {
        // 模拟数据库返回 null
        when(resultSet.getInt(1)).thenReturn(0);
        when(resultSet.wasNull()).thenReturn(true);
        
        TeamRole result = typeHandler.getNullableResult(resultSet, 1);
        
        assertNull(result);
    }

    @Test
    void testStatusCodesMapping() {
        // 验证状态码映射是否正确
        assertEquals(TeamRole.TEAM_CREATOR, TeamRole.StatusCodes.fromCode(100));
        assertEquals(TeamRole.TEAM_MEMBER, TeamRole.StatusCodes.fromCode(10));
        assertNull(TeamRole.StatusCodes.fromCode(999)); // 无效代码
        assertNull(TeamRole.StatusCodes.fromCode(null)); // null 代码
        
        assertEquals(Integer.valueOf(100), TeamRole.StatusCodes.toCode(TeamRole.TEAM_CREATOR));
        assertEquals(Integer.valueOf(10), TeamRole.StatusCodes.toCode(TeamRole.TEAM_MEMBER));
    }
}
