package com.teammanage.entity;

import java.time.LocalDateTime;

import org.apache.ibatis.type.JdbcType;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.teammanage.enums.TeamRole;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 团队成员实体类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("team_member")
public class TeamMember extends BaseEntity {

    /**
     * 团队ID
     */
    @NotNull(message = "团队ID不能为空")
    private Long teamId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long accountId;

    /**
     * 团队角色
     */
    @TableField(typeHandler = TeamRole.TeamRoleTypeHandler.class)
    private TeamRole role;

    /**
     * 分配时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime assignedAt;

    /**
     * 最后访问时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastAccessTime;

    /**
     * 账号状态
     */
    private Boolean isActive;

    /**
     * 删除标记
     */
    @TableLogic
    private Boolean isDeleted;



    // 角色相关的便利方法

    /**
     * 检查是否可以管理团队
     *
     * @return 是否可以管理团队
     */
    public boolean canManageTeam() {
        return role != null && role.canManageTeam();
    }

    /**
     * 检查是否可以管理成员
     *
     * @return 是否可以管理成员
     */
    public boolean canManageMembers() {
        return role != null && role.canManageMembers();
    }

    /**
     * 检查是否可以访问数据
     *
     * @return 是否可以访问数据
     */
    public boolean canAccessData() {
        return role != null && role.canAccessData();
    }

    /**
     * 检查角色权限是否高于或等于指定角色
     *
     * @param other 要比较的角色
     * @return 是否有更高或相等的权限
     */
    public boolean hasPermissionLevel(TeamRole other) {
        return role != null && role.hasPermissionLevel(other);
    }

    // 团队访问控制相关的便利方法

    /**
     * 检查成员是否处于活跃状态
     *
     * @return 是否活跃
     */
    public boolean isActiveStatus() {
        return Boolean.TRUE.equals(isActive);
    }

    /**
     * 检查成员是否被禁用
     *
     * @return 是否被禁用
     */
    public boolean isDisabled() {
        return !Boolean.TRUE.equals(isActive);
    }

    /**
     * 检查成员是否被停用（与禁用同义）
     *
     * @return 是否被停用
     */
    public boolean isDeactivated() {
        return isDisabled();
    }

    /**
     * 检查成员是否可以访问团队
     * 综合考虑删除状态和活跃状态
     *
     * @return 是否可以访问团队
     */
    public boolean canAccessTeam() {
        // 已删除的成员不能访问
        if (Boolean.TRUE.equals(isDeleted)) {
            return false;
        }

        // 非活跃状态的成员不能访问
        if (!isActiveStatus()) {
            return false;
        }

        return true;
    }

    /**
     * 检查成员是否应该在团队列表中显示
     * 即使被禁用也应该显示，但已删除的不显示
     *
     * @return 是否应该在列表中显示
     */
    public boolean shouldShowInTeamList() {
        // 已删除的成员不显示
        return !Boolean.TRUE.equals(isDeleted);
    }

    /**
     * 获取成员状态的描述信息
     *
     * @return 状态描述
     */
    public String getStatusDescription() {
        if (Boolean.TRUE.equals(isDeleted)) {
            return "已删除";
        }

        if (!isActiveStatus()) {
            return "已停用";
        }

        return "活跃";
    }

    /**
     * 获取访问被拒绝时的错误消息
     *
     * @return 错误消息
     */
    public String getAccessDeniedMessage() {
        if (Boolean.TRUE.equals(isDeleted)) {
            return "您已不是该团队的成员";
        }

        if (!isActiveStatus()) {
            return "您的账户已在此团队中被停用";
        }

        return "无法访问该团队";
    }

}
