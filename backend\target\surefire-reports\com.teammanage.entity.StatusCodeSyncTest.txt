-------------------------------------------------------------------------------
Test set: com.teammanage.entity.StatusCodeSyncTest
-------------------------------------------------------------------------------
Tests run: 4, Failures: 1, Errors: 1, Skipped: 0, Time elapsed: 0.053 s <<< FAILURE! -- in com.teammanage.entity.StatusCodeSyncTest
com.teammanage.entity.StatusCodeSyncTest.testBusinessLogicWithStatusCodes -- Time elapsed: 0.027 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "java.time.chrono.ChronoLocalDateTime.toLocalDate()" because "other" is null
	at java.base/java.time.chrono.ChronoLocalDateTime.isAfter(ChronoLocalDateTime.java:533)
	at java.base/java.time.LocalDateTime.isAfter(LocalDateTime.java:1854)
	at com.teammanage.entity.TeamInvitation.isExpired(TeamInvitation.java:138)
	at com.teammanage.entity.TeamInvitation.canBeResponded(TeamInvitation.java:145)
	at com.teammanage.entity.StatusCodeSyncTest.testBusinessLogicWithStatusCodes(StatusCodeSyncTest.java:173)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

com.teammanage.entity.StatusCodeSyncTest.testTeamMemberRoleSync -- Time elapsed: 0.007 s <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <null> but was: <false>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at org.junit.jupiter.api.AssertNull.failNotNull(AssertNull.java:50)
	at org.junit.jupiter.api.AssertNull.assertNull(AssertNull.java:35)
	at org.junit.jupiter.api.AssertNull.assertNull(AssertNull.java:30)
	at org.junit.jupiter.api.Assertions.assertNull(Assertions.java:279)
	at com.teammanage.entity.StatusCodeSyncTest.testTeamMemberRoleSync(StatusCodeSyncTest.java:63)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

